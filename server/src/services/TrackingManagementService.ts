import { PrismaClient } from '@prisma/client';
import { BeacukaiApiService } from './BeacukaiApiService';
import { GpsService } from './GpsService';

export interface TrackingStartData {
  esealId: string;

  // Journey info
  alamatAsal: string;
  alamatTujuan: string;
  latitudeAsal: string;
  latitudeTujuan: string;
  lokasiAsal: string;
  lokasiTujuan: string;
  longitudeAsal: string;
  longitudeTujuan: string;

  // Container info
  jnsKontainer: string;
  noKontainer: string;
  ukKontainer: string;

  // Vehicle info
  noPolisi: string;
  namaDriver: string;
  nomorTeleponDriver: string;

  // Documents (optional - will use from database if not provided)
  dokumen?: Array<{
    jenisMuat: string;
    jumlahKontainer: string;
    kodeDokumen: string;
    kodeKantor: string;
    nomorAju: string;
    nomorDokumen: string;
    tanggalDokumen: string;
  }>;
}

export interface TrackingStopData {
  esealId: string;
  alamatStop: string;
  latitudeStop: string;
  longitudeStop: string;
}

export class TrackingManagementService {
  private prisma: PrismaClient;
  private beacukaiService: BeacukaiApiService;
  private gpsService: GpsService;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.beacukaiService = new BeacukaiApiService(prisma);
    this.gpsService = new GpsService(prisma);
  }

  /**
   * Start tracking for an E-Seal device
   * 1. Validate device is registered and ready
   * 2. Create tracking session
   * 3. Call Beacukai tracking/start API
   * 4. Update device status
   */
  async startTracking(data: TrackingStartData): Promise<{
    trackingSession: any;
    beacukaiResponse: any;
    deviceStatus: any;
  }> {
    console.log(`🚀 Starting tracking for E-Seal: ${data.esealId}`);

    try {
      // 1. Get E-Seal and validate
      const eseal = await this.prisma.eSeal.findUnique({
        where: { id: data.esealId },
        include: {
          deviceStatus: true,
          dokumen: true
        }
      });

      if (!eseal) {
        throw new Error(`E-Seal not found: ${data.esealId}`);
      }

      if (!eseal.deviceStatus?.registeredWithBeacukai) {
        throw new Error(`E-Seal not registered with Beacukai: ${eseal.noEseal}`);
      }

      // Check if already tracking
      const activeSession = await this.prisma.trackingSession.findFirst({
        where: {
          esealId: data.esealId,
          sessionStatus: 'ACTIVE'
        }
      });

      if (activeSession) {
        throw new Error(`E-Seal is already being tracked: ${eseal.noEseal}`);
      }

      // 2. Create tracking session
      const trackingSession = await this.prisma.trackingSession.create({
        data: {
          esealId: data.esealId,
          sessionStatus: 'ACTIVE',
          startedAt: new Date()
        }
      });

      console.log(`✅ Tracking session created: ${trackingSession.id}`);

      // 3. Prepare documents for Beacukai API
      let dokumen = data.dokumen;
      if (!dokumen && eseal.dokumen.length > 0) {
        // Use documents from database
        dokumen = eseal.dokumen.map((doc: any) => ({
          jenisMuat: doc.jenisMuat,
          jumlahKontainer: doc.jumlahKontainer,
          kodeDokumen: doc.kodeDokumen,
          kodeKantor: doc.kodeKantor,
          nomorAju: doc.nomorAju,
          nomorDokumen: doc.nomorDokumen,
          tanggalDokumen: doc.tanggalDokumen
        }));
      }

      if (!dokumen || dokumen.length === 0) {
        console.warn(`⚠️ No documents found for E-Seal: ${eseal.noEseal}`);
        dokumen = []; // Empty array for now
      }

      // 4. Call Beacukai tracking/start API
      let beacukaiResponse;
      try {
        beacukaiResponse = await this.beacukaiService.startTracking({
          alamatAsal: data.alamatAsal,
          alamatTujuan: data.alamatTujuan,
          idVendor: eseal.idVendor,
          jnsKontainer: data.jnsKontainer,
          latitudeAsal: data.latitudeAsal,
          latitudeTujuan: data.latitudeTujuan,
          lokasiAsal: data.lokasiAsal,
          lokasiTujuan: data.lokasiTujuan,
          longitudeAsal: data.longitudeAsal,
          longitudeTujuan: data.longitudeTujuan,
          noImei: eseal.noImei,
          noEseal: eseal.noEseal,
          noKontainer: data.noKontainer,
          noPolisi: data.noPolisi,
          ukKontainer: data.ukKontainer,
          namaDriver: data.namaDriver,
          nomorTeleponDriver: data.nomorTeleponDriver,
          dokumen
        });

        console.log(`✅ Beacukai tracking started: ${eseal.noEseal}`);
      } catch (error) {
        console.error(`❌ Failed to start Beacukai tracking: ${eseal.noEseal}`, error);
        beacukaiResponse = {
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        };
      }

      // 5. Update tracking session with Beacukai response
      await this.prisma.trackingSession.update({
        where: { id: trackingSession.id },
        data: {
          beacukaiStartStatus: beacukaiResponse.status
        }
      });

      // 6. Update E-Seal with normalized number from Beacukai response if successful
      if (beacukaiResponse.status === 'success' && beacukaiResponse.item?.noEseal) {
        const normalizedNoEseal = beacukaiResponse.item.noEseal;
        if (normalizedNoEseal !== eseal.noEseal) {
          console.log(`🔄 Updating E-Seal number: ${eseal.noEseal} -> ${normalizedNoEseal}`);
          await this.prisma.eSeal.update({
            where: { id: data.esealId },
            data: {
              noEseal: normalizedNoEseal
            }
          });
        }
      }

      // 7. Update device status
      const deviceStatus = await this.prisma.deviceStatus.update({
        where: { esealId: data.esealId },
        data: {
          currentStatus: 'ACTIVE'
        }
      });

      console.log(`✅ Tracking started successfully: ${eseal.noEseal}`);

      return {
        trackingSession,
        beacukaiResponse,
        deviceStatus
      };

    } catch (error) {
      console.error(`💥 Failed to start tracking: ${data.esealId}`, error);
      throw error;
    }
  }

  /**
   * Stop tracking for an E-Seal device
   * 1. Find active tracking session
   * 2. Call Beacukai tracking/stop API
   * 3. Update tracking session and device status
   */
  async stopTracking(data: TrackingStopData): Promise<{
    trackingSession: any;
    beacukaiResponse: any;
    deviceStatus: any;
  }> {
    console.log(`🛑 Stopping tracking for E-Seal: ${data.esealId}`);

    try {
      // 1. Get E-Seal and active tracking session
      const eseal = await this.prisma.eSeal.findUnique({
        where: { id: data.esealId }
      });

      if (!eseal) {
        throw new Error(`E-Seal not found: ${data.esealId}`);
      }

      const activeSession = await this.prisma.trackingSession.findFirst({
        where: {
          esealId: data.esealId,
          sessionStatus: 'ACTIVE'
        }
      });

      if (!activeSession) {
        throw new Error(`No active tracking session found: ${eseal.noEseal}`);
      }

      // 2. Call Beacukai tracking/stop API
      let beacukaiResponse;
      try {
        beacukaiResponse = await this.beacukaiService.stopTracking({
          alamatStop: data.alamatStop,
          idVendor: eseal.idVendor,
          latitudeStop: data.latitudeStop,
          longitudeStop: data.longitudeStop,
          noImei: eseal.noImei,
          noEseal: eseal.noEseal,
          token: '919253c8-d0e1-4780-89d0-e91f77e89855' // Default vendor token
        });

        console.log(`✅ Beacukai tracking stopped: ${eseal.noEseal}`);
      } catch (error) {
        console.error(`❌ Failed to stop Beacukai tracking: ${eseal.noEseal}`, error);
        beacukaiResponse = {
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        };
      }

      // 3. Update tracking session
      const trackingSession = await this.prisma.trackingSession.update({
        where: { id: activeSession.id },
        data: {
          sessionStatus: 'STOPPED',
          stoppedAt: new Date(),
          beacukaiStopStatus: beacukaiResponse.status
        }
      });

      // 4. Update device status
      const deviceStatus = await this.prisma.deviceStatus.update({
        where: { esealId: data.esealId },
        data: {
          currentStatus: 'INACTIVE'
        }
      });

      console.log(`✅ Tracking stopped successfully: ${eseal.noEseal}`);

      return {
        trackingSession,
        beacukaiResponse,
        deviceStatus
      };

    } catch (error) {
      console.error(`💥 Failed to stop tracking: ${data.esealId}`, error);
      throw error;
    }
  }

  /**
   * Get tracking status for an E-Seal
   */
  async getTrackingStatus(esealId: string): Promise<{
    eseal: any;
    activeSession?: any;
    deviceStatus: any;
    beacukaiStatus?: any;
    recentPositions: any[];
  }> {
    const eseal = await this.prisma.eSeal.findUnique({
      where: { id: esealId },
      include: {
        deviceStatus: true,
        trackingSessions: {
          where: { sessionStatus: 'ACTIVE' },
          take: 1
        }
      }
    });

    if (!eseal) {
      throw new Error(`E-Seal not found: ${esealId}`);
    }

    const activeSession = eseal.trackingSessions[0];

    // Get recent position updates
    const recentPositions = await this.prisma.trackingLog.findMany({
      where: { esealId },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    // Get Beacukai tracking status if actively tracking
    let beacukaiStatus;
    if (activeSession) {
      try {
        beacukaiStatus = await this.beacukaiService.getTrackingStatus(
          eseal.idVendor,
          eseal.noEseal
        );
      } catch (error) {
        console.error(`❌ Failed to get Beacukai status: ${eseal.noEseal}`, error);
        beacukaiStatus = {
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    return {
      eseal,
      activeSession,
      deviceStatus: eseal.deviceStatus,
      beacukaiStatus,
      recentPositions
    };
  }

  /**
   * Get all active tracking sessions
   */
  async getActiveTrackingSessions(): Promise<any[]> {
    return await this.prisma.trackingSession.findMany({
      where: { sessionStatus: 'ACTIVE' },
      include: {
        eseal: {
          include: {
            deviceStatus: true
          }
        }
      },
      orderBy: { startedAt: 'desc' }
    });
  }

  /**
   * Update device status (for manual status changes)
   */
  async updateDeviceStatus(esealId: string, status: string): Promise<any> {
    const eseal = await this.prisma.eSeal.findUnique({
      where: { id: esealId }
    });

    if (!eseal) {
      throw new Error(`E-Seal not found: ${esealId}`);
    }

    console.log(`🔄 Updating device status: ${eseal.noEseal} -> ${status}`);

    // Call Beacukai API
    const beacukaiResponse = await this.beacukaiService.updateStatusDevice({
      idVendor: eseal.idVendor,
      noImei: eseal.noImei,
      status
    });

    // Update local database
    await this.prisma.deviceStatus.update({
      where: { esealId },
      data: {
        currentStatus: status,
        updatedAt: new Date()
      }
    });

    return beacukaiResponse;
  }
}
